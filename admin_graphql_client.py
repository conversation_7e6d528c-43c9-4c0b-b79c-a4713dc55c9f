#!/usr/bin/env python3
"""
Firespoon GraphQL Admin Client
通过 Python 程序获得 admin 权限并进行 GraphQL 访问的示例
"""

import requests
import json
from typing import Dict, Any, Optional

class FirespoonAdminClient:
    def __init__(self, graphql_url: str):
        """
        初始化 Firespoon Admin 客户端
        
        Args:
            graphql_url: GraphQL 端点 URL，例如 'http://localhost:4000/graphql'
        """
        self.graphql_url = graphql_url
        self.admin_token = None
        self.session = requests.Session()
        
    def login_as_admin(self, email: str = "<EMAIL>", password: str = "123123") -> Dict[str, Any]:
        """
        使用 ownerLogin 获取 admin 权限
        
        Args:
            email: admin 邮箱，默认为种子数据中的 <EMAIL>
            password: admin 密码，默认为种子数据中的 123123
            
        Returns:
            登录响应数据，包含 token 和用户信息
        """
        mutation = """
        mutation OwnerLogin($email: String!, $password: String!) {
            ownerLogin(email: $email, password: $password) {
                userId
                token
                email
                userType
                restaurants {
                    _id
                    name
                }
            }
        }
        """
        
        variables = {
            "email": email,
            "password": password
        }
        
        response = self._execute_graphql(mutation, variables)
        
        if response.get('data', {}).get('ownerLogin'):
            login_data = response['data']['ownerLogin']
            self.admin_token = login_data['token']
            
            # 验证是否获得了 admin 权限
            if login_data.get('userType') == 'ADMIN':
                print(f"✅ 成功获得 admin 权限！")
                print(f"   用户ID: {login_data['userId']}")
                print(f"   邮箱: {login_data['email']}")
                print(f"   用户类型: {login_data['userType']}")
                print(f"   Token: {self.admin_token[:50]}...")
            else:
                print(f"⚠️  登录成功但不是 admin 用户，用户类型: {login_data.get('userType')}")
                
            return login_data
        else:
            error_msg = response.get('errors', [{'message': '未知错误'}])[0]['message']
            raise Exception(f"登录失败: {error_msg}")
    
    def _execute_graphql(self, query: str, variables: Optional[Dict] = None, use_auth: bool = False) -> Dict[str, Any]:
        """
        执行 GraphQL 请求
        
        Args:
            query: GraphQL 查询或变更
            variables: 查询变量
            use_auth: 是否使用认证 token
            
        Returns:
            GraphQL 响应数据
        """
        headers = {
            'Content-Type': 'application/json',
        }
        
        # 如果需要认证且有 token，添加 Authorization 头
        if use_auth and self.admin_token:
            headers['Authorization'] = f'Bearer {self.admin_token}'
        
        payload = {
            'query': query,
            'variables': variables or {}
        }
        
        try:
            response = self.session.post(
                self.graphql_url,
                headers=headers,
                json=payload,
                timeout=30
            )
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            raise Exception(f"GraphQL 请求失败: {e}")
    
    def get_configuration(self) -> Dict[str, Any]:
        """
        获取系统配置（需要 admin 权限）
        """
        if not self.admin_token:
            raise Exception("请先登录获取 admin token")
            
        query = """
        query GetConfiguration {
            configuration {
                _id
                email
                emailName
                password
                enableEmail
                clientId
                clientSecret
                sandbox
                publishableKey
                secretKey
                currency
                currencySymbol
                deliveryRate
                twilioAccountSid
                twilioAuthToken
                twilioPhoneNumber
                firebaseKey
                authDomain
                projectId
                storageBucket
                msgSenderId
                appId
                measurementId
                isPaidVersion
                skipMobileVerification
                skipEmailVerification
                enableAdminDemo
                enableRestaurantDemo
                enableRiderDemo
            }
        }
        """
        
        response = self._execute_graphql(query, use_auth=True)
        
        if response.get('data', {}).get('configuration'):
            print("✅ 成功获取系统配置（admin 权限验证通过）")
            return response['data']['configuration']
        else:
            error_msg = response.get('errors', [{'message': '未知错误'}])[0]['message']
            raise Exception(f"获取配置失败: {error_msg}")
    
    def get_all_restaurants(self) -> Dict[str, Any]:
        """
        获取所有餐厅信息（admin 可以查看所有餐厅）
        """
        if not self.admin_token:
            raise Exception("请先登录获取 admin token")
            
        query = """
        query GetAllRestaurants {
            restaurants {
                _id
                name
                address
                phone
                isAvailable
                commissionRate
                owner {
                    _id
                    email
                    userType
                }
            }
        }
        """
        
        response = self._execute_graphql(query, use_auth=True)
        
        if response.get('data', {}).get('restaurants'):
            restaurants = response['data']['restaurants']
            print(f"✅ 成功获取 {len(restaurants)} 个餐厅信息")
            return restaurants
        else:
            error_msg = response.get('errors', [{'message': '未知错误'}])[0]['message']
            raise Exception(f"获取餐厅信息失败: {error_msg}")
    
    def create_vendor(self, email: str, password: str) -> Dict[str, Any]:
        """
        创建新的供应商（需要 admin 权限）
        """
        if not self.admin_token:
            raise Exception("请先登录获取 admin token")
            
        mutation = """
        mutation CreateVendor($vendorInput: VendorInput!) {
            createVendor(vendorInput: $vendorInput) {
                _id
                email
                userType
            }
        }
        """
        
        variables = {
            "vendorInput": {
                "email": email,
                "password": password
            }
        }
        
        response = self._execute_graphql(mutation, variables, use_auth=True)
        
        if response.get('data', {}).get('createVendor'):
            vendor_data = response['data']['createVendor']
            print(f"✅ 成功创建供应商: {vendor_data['email']}")
            return vendor_data
        else:
            error_msg = response.get('errors', [{'message': '未知错误'}])[0]['message']
            raise Exception(f"创建供应商失败: {error_msg}")


def main():
    """
    使用示例
    """
    # 替换为你的 GraphQL 端点 URL
    GRAPHQL_URL = "http://localhost:4000/graphql"
    
    # 创建客户端
    client = FirespoonAdminClient(GRAPHQL_URL)
    
    try:
        print("🚀 开始获取 admin 权限...")
        
        # 1. 登录获取 admin token
        login_result = client.login_as_admin()
        
        # 2. 测试 admin 权限 - 获取系统配置
        print("\n📋 测试 admin 权限 - 获取系统配置...")
        config = client.get_configuration()
        print(f"   系统货币: {config.get('currency', 'N/A')}")
        print(f"   配送费率: {config.get('deliveryRate', 'N/A')}")
        print(f"   启用邮件: {config.get('enableEmail', 'N/A')}")
        
        # 3. 获取所有餐厅信息
        print("\n🏪 获取所有餐厅信息...")
        restaurants = client.get_all_restaurants()
        for restaurant in restaurants[:3]:  # 只显示前3个
            print(f"   餐厅: {restaurant['name']} - {restaurant['address']}")
        
        # 4. 创建新供应商（可选）
        # print("\n👤 创建新供应商...")
        # new_vendor = client.create_vendor("<EMAIL>", "password123")
        
        print("\n✅ 所有操作完成！admin 权限验证成功。")
        
    except Exception as e:
        print(f"❌ 错误: {e}")


if __name__ == "__main__":
    main()
