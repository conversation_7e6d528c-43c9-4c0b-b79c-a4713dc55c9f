/**
 * Firespoon 操作审计日志系统
 * 记录所有重要的系统操作，用于安全监控和合规要求
 */

const mongoose = require('mongoose');
const logger = require('../helpers/logger');

// 审计日志数据模型
const auditLogSchema = new mongoose.Schema({
  // 基本信息
  timestamp: {
    type: Date,
    default: Date.now,
    index: true
  },
  
  // 操作者信息
  actor: {
    userId: { type: String, required: true },
    userType: { type: String, required: true }, // ADMIN, RESTAURANT, CUSTOMER, RIDER
    email: { type: String },
    name: { type: String }
  },
  
  // 操作信息
  action: {
    type: { type: String, required: true }, // CREATE, UPDATE, DELETE, LOGIN, LOGOUT
    category: { type: String, required: true }, // AUTH, ORDER, RESTAURANT, USER, CONFIG
    operation: { type: String, required: true }, // 具体操作名称
    description: { type: String } // 操作描述
  },
  
  // 目标资源
  target: {
    resourceType: { type: String }, // Order, Restaurant, User, Configuration
    resourceId: { type: String },
    resourceName: { type: String }
  },
  
  // 操作详情
  details: {
    oldValues: { type: mongoose.Schema.Types.Mixed }, // 修改前的值
    newValues: { type: mongoose.Schema.Types.Mixed }, // 修改后的值
    changes: [{ // 具体变更字段
      field: String,
      oldValue: mongoose.Schema.Types.Mixed,
      newValue: mongoose.Schema.Types.Mixed
    }]
  },
  
  // 请求信息
  request: {
    ip: { type: String },
    userAgent: { type: String },
    method: { type: String }, // GraphQL mutation/query name
    endpoint: { type: String }
  },
  
  // 结果信息
  result: {
    status: { type: String, enum: ['SUCCESS', 'FAILURE', 'ERROR'], required: true },
    errorMessage: { type: String },
    errorCode: { type: String }
  },
  
  // 风险级别
  riskLevel: {
    type: String,
    enum: ['LOW', 'MEDIUM', 'HIGH', 'CRITICAL'],
    default: 'LOW'
  },
  
  // 合规标签
  complianceFlags: [{
    type: String // GDPR, PCI_DSS, SOX等
  }]
}, {
  timestamps: true,
  // 创建索引以提高查询性能
  index: {
    'timestamp': -1,
    'actor.userId': 1,
    'action.category': 1,
    'riskLevel': 1
  }
});

const AuditLog = mongoose.model('AuditLog', auditLogSchema);

/**
 * 审计日志服务类
 */
class AuditLogService {
  
  /**
   * 记录审计日志
   */
  static async log(auditData) {
    try {
      const auditLog = new AuditLog(auditData);
      await auditLog.save();
      
      // 高风险操作立即告警
      if (auditData.riskLevel === 'CRITICAL' || auditData.riskLevel === 'HIGH') {
        await this.sendSecurityAlert(auditData);
      }
      
      return auditLog;
    } catch (error) {
      logger.error('Failed to save audit log:', error);
      // 审计日志失败不应该影响业务操作
    }
  }
  
  /**
   * 认证相关操作审计
   */
  static async logAuth(action, actor, request, result, details = {}) {
    const riskLevel = this.calculateAuthRiskLevel(action, result, details);
    
    return await this.log({
      actor,
      action: {
        type: action.toUpperCase(),
        category: 'AUTH',
        operation: action,
        description: `User ${action} attempt`
      },
      request,
      result,
      riskLevel,
      details
    });
  }
  
  /**
   * 管理员操作审计
   */
  static async logAdminAction(operation, actor, target, request, result, changes = {}) {
    return await this.log({
      actor,
      action: {
        type: 'ADMIN_ACTION',
        category: 'ADMIN',
        operation,
        description: `Admin performed ${operation}`
      },
      target,
      request,
      result,
      details: { changes },
      riskLevel: 'HIGH', // 管理员操作默认高风险
      complianceFlags: ['SOX', 'AUDIT_TRAIL']
    });
  }
  
  /**
   * 订单操作审计
   */
  static async logOrderAction(action, actor, orderId, request, result, details = {}) {
    const riskLevel = action === 'refund' ? 'HIGH' : 'MEDIUM';
    
    return await this.log({
      actor,
      action: {
        type: action.toUpperCase(),
        category: 'ORDER',
        operation: action,
        description: `Order ${action} operation`
      },
      target: {
        resourceType: 'Order',
        resourceId: orderId
      },
      request,
      result,
      details,
      riskLevel,
      complianceFlags: ['PCI_DSS'] // 涉及支付的操作
    });
  }
  
  /**
   * 配置变更审计
   */
  static async logConfigChange(configType, actor, request, result, oldValues, newValues) {
    const changes = this.calculateChanges(oldValues, newValues);
    
    return await this.log({
      actor,
      action: {
        type: 'UPDATE',
        category: 'CONFIG',
        operation: 'updateConfiguration',
        description: `Configuration ${configType} updated`
      },
      target: {
        resourceType: 'Configuration',
        resourceId: configType
      },
      request,
      result,
      details: { oldValues, newValues, changes },
      riskLevel: 'HIGH', // 配置变更高风险
      complianceFlags: ['SOX', 'CHANGE_MANAGEMENT']
    });
  }
  
  /**
   * 用户数据访问审计（GDPR合规）
   */
  static async logDataAccess(dataType, actor, targetUserId, request, result) {
    return await this.log({
      actor,
      action: {
        type: 'READ',
        category: 'DATA_ACCESS',
        operation: 'accessUserData',
        description: `Accessed user ${dataType} data`
      },
      target: {
        resourceType: 'UserData',
        resourceId: targetUserId
      },
      request,
      result,
      riskLevel: 'MEDIUM',
      complianceFlags: ['GDPR', 'PRIVACY']
    });
  }
  
  /**
   * 计算认证操作风险级别
   */
  static calculateAuthRiskLevel(action, result, details) {
    if (result.status === 'FAILURE') {
      if (details.failureCount && details.failureCount > 3) {
        return 'HIGH'; // 多次失败登录
      }
      return 'MEDIUM';
    }
    
    if (action === 'login' && details.userType === 'ADMIN') {
      return 'HIGH'; // 管理员登录
    }
    
    return 'LOW';
  }
  
  /**
   * 计算字段变更
   */
  static calculateChanges(oldValues, newValues) {
    const changes = [];
    
    for (const [key, newValue] of Object.entries(newValues || {})) {
      const oldValue = oldValues?.[key];
      if (oldValue !== newValue) {
        changes.push({
          field: key,
          oldValue,
          newValue
        });
      }
    }
    
    return changes;
  }
  
  /**
   * 发送安全告警
   */
  static async sendSecurityAlert(auditData) {
    // 实现安全告警逻辑
    logger.warn('Security Alert:', {
      operation: auditData.action.operation,
      actor: auditData.actor.email,
      riskLevel: auditData.riskLevel,
      timestamp: auditData.timestamp
    });
    
    // 可以集成邮件、Slack、短信等告警方式
  }
  
  /**
   * 查询审计日志
   */
  static async query(filters = {}, options = {}) {
    const {
      userId,
      category,
      riskLevel,
      startDate,
      endDate,
      limit = 100,
      offset = 0
    } = { ...filters, ...options };
    
    const query = {};
    
    if (userId) query['actor.userId'] = userId;
    if (category) query['action.category'] = category;
    if (riskLevel) query.riskLevel = riskLevel;
    if (startDate || endDate) {
      query.timestamp = {};
      if (startDate) query.timestamp.$gte = new Date(startDate);
      if (endDate) query.timestamp.$lte = new Date(endDate);
    }
    
    return await AuditLog.find(query)
      .sort({ timestamp: -1 })
      .limit(limit)
      .skip(offset)
      .lean();
  }
  
  /**
   * 生成审计报告
   */
  static async generateReport(startDate, endDate) {
    const pipeline = [
      {
        $match: {
          timestamp: {
            $gte: new Date(startDate),
            $lte: new Date(endDate)
          }
        }
      },
      {
        $group: {
          _id: {
            category: '$action.category',
            riskLevel: '$riskLevel'
          },
          count: { $sum: 1 },
          users: { $addToSet: '$actor.userId' }
        }
      },
      {
        $sort: { count: -1 }
      }
    ];
    
    return await AuditLog.aggregate(pipeline);
  }
}

module.exports = {
  AuditLog,
  AuditLogService
};
