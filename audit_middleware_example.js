/**
 * 审计日志中间件集成示例
 * 展示如何在现有的GraphQL resolvers中集成审计日志
 */

const { AuditLogService } = require('./audit_log_system');

/**
 * GraphQL审计中间件
 */
const auditMiddleware = (originalResolver, auditConfig) => {
  return async (parent, args, context, info) => {
    const startTime = Date.now();
    let result;
    let error;
    
    try {
      // 执行原始resolver
      result = await originalResolver(parent, args, context, info);
      
      // 记录成功操作
      await logOperation(auditConfig, args, context, info, {
        status: 'SUCCESS',
        duration: Date.now() - startTime
      }, result);
      
      return result;
    } catch (err) {
      error = err;
      
      // 记录失败操作
      await logOperation(auditConfig, args, context, info, {
        status: 'FAILURE',
        errorMessage: err.message,
        duration: Date.now() - startTime
      });
      
      throw err;
    }
  };
};

/**
 * 记录操作日志
 */
async function logOperation(auditConfig, args, context, info, result, data = null) {
  const { req } = context;
  
  // 构建审计数据
  const auditData = {
    actor: {
      userId: req.userId || 'anonymous',
      userType: req.userType || 'unknown',
      email: req.email
    },
    action: {
      type: auditConfig.actionType,
      category: auditConfig.category,
      operation: info.fieldName,
      description: auditConfig.description || `${info.fieldName} operation`
    },
    request: {
      ip: req.ip || req.connection.remoteAddress,
      userAgent: req.get('User-Agent'),
      method: info.fieldName,
      endpoint: 'GraphQL'
    },
    result,
    riskLevel: auditConfig.riskLevel || 'LOW'
  };
  
  // 添加目标资源信息
  if (auditConfig.getTarget) {
    auditData.target = auditConfig.getTarget(args, data);
  }
  
  // 添加详细信息
  if (auditConfig.getDetails) {
    auditData.details = auditConfig.getDetails(args, data);
  }
  
  // 记录日志
  await AuditLogService.log(auditData);
}

/**
 * 改进的ownerLogin resolver（集成审计日志）
 */
const auditedOwnerLogin = auditMiddleware(
  // 原始resolver
  async (_, { email, password }, context) => {
    console.log('ownerLogin');
    const owner = await Owner.findOne({ email: email });
    if (!owner) {
      throw new Error('User does not exist!');
    }
    const isEqual = await bcrypt.compare(password, owner.password);
    if (!isEqual) {
      throw new Error('Invalid credentials!');
    }
    
    const token = jwt.sign(
      {
        userId: owner.id,
        email: owner.email,
        userType: owner.userType
      },
      config.JWT_SECRET,
      { expiresIn: '30m' } // 添加过期时间
    );
    
    const result = await transformOwner(owner);
    return {
      ...result,
      token: token
    };
  },
  // 审计配置
  {
    actionType: 'LOGIN',
    category: 'AUTH',
    description: 'Owner login attempt',
    riskLevel: 'HIGH', // 管理员登录高风险
    getDetails: (args, data) => ({
      email: args.email,
      userType: data?.userType,
      loginTime: new Date()
    })
  }
);

/**
 * 改进的配置更新resolver（集成审计日志）
 */
const auditedSaveConfiguration = auditMiddleware(
  // 原始resolver
  async (_, args, { req }) => {
    if (!req.isAuth || req.userType !== 'ADMIN') {
      throw new Error('Unauthorized');
    }
    
    // 获取旧配置
    const oldConfig = await Configuration.findOne();
    
    // 更新配置
    const updatedConfig = await Configuration.findOneAndUpdate(
      {},
      args.configurationInput,
      { new: true, upsert: true }
    );
    
    return updatedConfig;
  },
  // 审计配置
  {
    actionType: 'UPDATE',
    category: 'CONFIG',
    description: 'System configuration updated',
    riskLevel: 'CRITICAL', // 配置变更极高风险
    getTarget: (args) => ({
      resourceType: 'Configuration',
      resourceId: 'system_config'
    }),
    getDetails: async (args) => {
      const oldConfig = await Configuration.findOne();
      return {
        oldValues: oldConfig?.toObject(),
        newValues: args.configurationInput,
        changes: calculateConfigChanges(oldConfig, args.configurationInput)
      };
    }
  }
);

/**
 * 订单退款resolver（集成审计日志）
 */
const auditedRefundOrder = auditMiddleware(
  // 原始resolver
  async (_, { orderId, amount, reason }, { req }) => {
    if (!req.isAuth) {
      throw new Error('Unauthorized');
    }
    
    const order = await Order.findById(orderId);
    if (!order) {
      throw new Error('Order not found');
    }
    
    // 执行退款逻辑
    const refund = await processRefund(order, amount, reason);
    
    return refund;
  },
  // 审计配置
  {
    actionType: 'REFUND',
    category: 'ORDER',
    description: 'Order refund processed',
    riskLevel: 'HIGH', // 退款操作高风险
    getTarget: (args) => ({
      resourceType: 'Order',
      resourceId: args.orderId
    }),
    getDetails: (args) => ({
      refundAmount: args.amount,
      reason: args.reason,
      refundTime: new Date()
    })
  }
);

/**
 * 用户数据查询resolver（GDPR合规）
 */
const auditedGetUserData = auditMiddleware(
  // 原始resolver
  async (_, { userId }, { req }) => {
    if (!req.isAuth) {
      throw new Error('Unauthorized');
    }
    
    // 检查权限：只能查看自己的数据或管理员可以查看所有数据
    if (req.userId !== userId && req.userType !== 'ADMIN') {
      throw new Error('Access denied');
    }
    
    const user = await User.findById(userId);
    return user;
  },
  // 审计配置
  {
    actionType: 'READ',
    category: 'DATA_ACCESS',
    description: 'User personal data accessed',
    riskLevel: 'MEDIUM',
    getTarget: (args) => ({
      resourceType: 'UserData',
      resourceId: args.userId
    }),
    getDetails: (args, data) => ({
      accessedUserId: args.userId,
      dataFields: Object.keys(data || {}),
      gdprCompliance: true
    })
  }
);

/**
 * 计算配置变更
 */
function calculateConfigChanges(oldConfig, newConfig) {
  const changes = [];
  
  for (const [key, newValue] of Object.entries(newConfig)) {
    const oldValue = oldConfig?.[key];
    if (oldValue !== newValue) {
      changes.push({
        field: key,
        oldValue,
        newValue,
        sensitive: isSensitiveField(key) // 标记敏感字段
      });
    }
  }
  
  return changes;
}

/**
 * 判断是否为敏感字段
 */
function isSensitiveField(fieldName) {
  const sensitiveFields = [
    'password', 'secret', 'key', 'token',
    'stripe', 'paypal', 'twilio', 'firebase'
  ];
  
  return sensitiveFields.some(sensitive => 
    fieldName.toLowerCase().includes(sensitive)
  );
}

/**
 * 审计日志查询API（管理员专用）
 */
const getAuditLogs = async (_, { filters, pagination }, { req }) => {
  if (!req.isAuth || req.userType !== 'ADMIN') {
    throw new Error('Admin access required');
  }
  
  const logs = await AuditLogService.query(filters, pagination);
  
  // 记录审计日志查询操作
  await AuditLogService.logAdminAction(
    'viewAuditLogs',
    {
      userId: req.userId,
      userType: req.userType,
      email: req.email
    },
    {
      resourceType: 'AuditLog',
      resourceId: 'query'
    },
    {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      method: 'getAuditLogs',
      endpoint: 'GraphQL'
    },
    { status: 'SUCCESS' },
    { filters, pagination }
  );
  
  return logs;
};

module.exports = {
  auditMiddleware,
  auditedOwnerLogin,
  auditedSaveConfiguration,
  auditedRefundOrder,
  auditedGetUserData,
  getAuditLogs
};
