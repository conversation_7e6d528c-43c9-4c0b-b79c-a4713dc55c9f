/**
 * 安全的双Token认证方案示例
 * 解决JWT过期时间与用户体验的平衡问题
 */

const jwt = require('jsonwebtoken');
const crypto = require('crypto');

class SecureTokenManager {
  constructor(config) {
    this.jwtSecret = config.JWT_SECRET;
    this.refreshSecret = config.REFRESH_SECRET || config.JWT_SECRET + '_refresh';
    
    // Token 配置
    this.accessTokenExpiry = '30m';  // 30分钟
    this.refreshTokenExpiry = '7d';   // 7天
  }

  /**
   * 生成双Token
   */
  generateTokenPair(payload) {
    // 1. 生成短期 Access Token
    const accessToken = jwt.sign(
      {
        ...payload,
        type: 'access'
      },
      this.jwtSecret,
      { 
        expiresIn: this.accessTokenExpiry,
        issuer: 'firespoon-api',
        audience: 'firespoon-client'
      }
    );

    // 2. 生成长期 Refresh Token
    const refreshTokenId = crypto.randomUUID();
    const refreshToken = jwt.sign(
      {
        userId: payload.userId,
        tokenId: refreshTokenId,
        type: 'refresh'
      },
      this.refreshSecret,
      { 
        expiresIn: this.refreshTokenExpiry,
        issuer: 'firespoon-api',
        audience: 'firespoon-client'
      }
    );

    return {
      accessToken,
      refreshToken,
      refreshTokenId,
      expiresIn: 30 * 60, // 30分钟（秒）
      tokenType: 'Bearer'
    };
  }

  /**
   * 验证 Access Token
   */
  verifyAccessToken(token) {
    try {
      const decoded = jwt.verify(token, this.jwtSecret);
      if (decoded.type !== 'access') {
        throw new Error('Invalid token type');
      }
      return decoded;
    } catch (error) {
      if (error.name === 'TokenExpiredError') {
        throw new Error('Access token expired');
      }
      throw new Error('Invalid access token');
    }
  }

  /**
   * 验证 Refresh Token
   */
  verifyRefreshToken(token) {
    try {
      const decoded = jwt.verify(token, this.refreshSecret);
      if (decoded.type !== 'refresh') {
        throw new Error('Invalid token type');
      }
      return decoded;
    } catch (error) {
      if (error.name === 'TokenExpiredError') {
        throw new Error('Refresh token expired');
      }
      throw new Error('Invalid refresh token');
    }
  }

  /**
   * 刷新 Access Token
   */
  async refreshAccessToken(refreshToken, getUserData) {
    // 1. 验证 Refresh Token
    const decoded = this.verifyRefreshToken(refreshToken);
    
    // 2. 检查 Refresh Token 是否在黑名单中（需要数据库支持）
    const isBlacklisted = await this.isRefreshTokenBlacklisted(decoded.tokenId);
    if (isBlacklisted) {
      throw new Error('Refresh token has been revoked');
    }

    // 3. 获取最新用户数据
    const userData = await getUserData(decoded.userId);
    if (!userData) {
      throw new Error('User not found');
    }

    // 4. 生成新的 Access Token（保持原 Refresh Token）
    const accessToken = jwt.sign(
      {
        userId: userData.userId,
        email: userData.email,
        userType: userData.userType,
        type: 'access'
      },
      this.jwtSecret,
      { 
        expiresIn: this.accessTokenExpiry,
        issuer: 'firespoon-api',
        audience: 'firespoon-client'
      }
    );

    return {
      accessToken,
      expiresIn: 30 * 60,
      tokenType: 'Bearer'
    };
  }

  /**
   * 撤销 Refresh Token
   */
  async revokeRefreshToken(refreshToken) {
    const decoded = this.verifyRefreshToken(refreshToken);
    // 将 tokenId 加入黑名单（需要数据库支持）
    await this.addToBlacklist(decoded.tokenId);
  }

  /**
   * 检查 Refresh Token 是否在黑名单中
   * 需要在数据库中实现
   */
  async isRefreshTokenBlacklisted(tokenId) {
    // 示例实现 - 需要连接实际数据库
    // const BlacklistedToken = require('../models/blacklistedToken');
    // return await BlacklistedToken.exists({ tokenId });
    return false; // 临时返回
  }

  /**
   * 将 Refresh Token 加入黑名单
   */
  async addToBlacklist(tokenId) {
    // 示例实现 - 需要连接实际数据库
    // const BlacklistedToken = require('../models/blacklistedToken');
    // await BlacklistedToken.create({ tokenId, createdAt: new Date() });
  }
}

/**
 * 改进的登录 Resolver 示例
 */
const improvedOwnerLogin = async (_, { email, password }, context) => {
  console.log('ownerLogin with secure tokens');
  
  // 1. 验证用户凭据
  const owner = await Owner.findOne({ email: email });
  if (!owner) {
    throw new Error('User does not exist!');
  }
  
  const isEqual = await bcrypt.compare(password, owner.password);
  if (!isEqual) {
    throw new Error('Invalid credentials!');
  }

  // 2. 生成双Token
  const tokenManager = new SecureTokenManager(config);
  const tokens = tokenManager.generateTokenPair({
    userId: owner.id,
    email: owner.email,
    userType: owner.userType
  });

  // 3. 存储 Refresh Token ID（用于撤销）
  // await RefreshToken.create({
  //   tokenId: tokens.refreshTokenId,
  //   userId: owner.id,
  //   expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7天
  // });

  // 4. 返回结果
  const result = await transformOwner(owner);
  return {
    ...result,
    accessToken: tokens.accessToken,
    refreshToken: tokens.refreshToken,
    expiresIn: tokens.expiresIn,
    tokenType: tokens.tokenType
  };
};

/**
 * Token 刷新 Resolver
 */
const refreshToken = async (_, { refreshToken }, context) => {
  const tokenManager = new SecureTokenManager(config);
  
  // 获取用户数据的函数
  const getUserData = async (userId) => {
    const owner = await Owner.findById(userId);
    return owner ? {
      userId: owner.id,
      email: owner.email,
      userType: owner.userType
    } : null;
  };

  const result = await tokenManager.refreshAccessToken(refreshToken, getUserData);
  return result;
};

/**
 * 登出 Resolver（撤销 Refresh Token）
 */
const logout = async (_, { refreshToken }, context) => {
  const tokenManager = new SecureTokenManager(config);
  await tokenManager.revokeRefreshToken(refreshToken);
  return { success: true };
};

module.exports = {
  SecureTokenManager,
  improvedOwnerLogin,
  refreshToken,
  logout
};
