/**
 * Sentry与审计日志集成方案
 * 将高风险操作和异常行为自动发送到Sentry进行监控和告警
 */

const Sentry = require('@sentry/node');
const { AuditLogService } = require('./audit_log_system');

/**
 * Sentry审计集成服务
 */
class SentryAuditIntegration {
  
  /**
   * 初始化Sentry配置
   */
  static init() {
    Sentry.init({
      dsn: process.env.SENTRY_DSN,
      environment: process.env.NODE_ENV,
      
      // 自定义标签
      initialScope: {
        tags: {
          component: 'audit-system',
          service: 'firespoon-api'
        }
      },
      
      // 采样率配置
      tracesSampleRate: process.env.NODE_ENV === 'production' ? 0.1 : 1.0,
      
      // 过滤敏感信息
      beforeSend(event) {
        // 移除敏感数据
        if (event.extra) {
          delete event.extra.password;
          delete event.extra.token;
          delete event.extra.secret;
        }
        return event;
      }
    });
  }
  
  /**
   * 发送安全事件到Sentry
   */
  static async sendSecurityEvent(auditData, severity = 'warning') {
    const { action, actor, request, result, riskLevel } = auditData;
    
    // 根据风险级别确定Sentry严重程度
    const sentryLevel = this.mapRiskToSentryLevel(riskLevel);
    
    Sentry.withScope((scope) => {
      // 设置用户信息
      scope.setUser({
        id: actor.userId,
        email: actor.email,
        userType: actor.userType
      });
      
      // 设置标签
      scope.setTags({
        operation: action.operation,
        category: action.category,
        riskLevel: riskLevel,
        status: result.status,
        userType: actor.userType
      });
      
      // 设置上下文
      scope.setContext('audit_details', {
        operation: action.operation,
        description: action.description,
        timestamp: auditData.timestamp,
        ip: request.ip,
        userAgent: request.userAgent
      });
      
      // 设置指纹（用于事件去重）
      scope.setFingerprint([
        action.operation,
        actor.userId,
        riskLevel
      ]);
      
      // 发送事件
      Sentry.captureMessage(
        `Security Event: ${action.description}`,
        sentryLevel
      );
    });
  }
  
  /**
   * 发送异常操作告警
   */
  static async sendAnomalyAlert(anomalyType, details) {
    Sentry.withScope((scope) => {
      scope.setTags({
        anomaly_type: anomalyType,
        alert_type: 'security_anomaly'
      });
      
      scope.setContext('anomaly_details', details);
      
      Sentry.captureException(
        new Error(`Security Anomaly Detected: ${anomalyType}`)
      );
    });
  }
  
  /**
   * 批量失败登录告警
   */
  static async sendBruteForceAlert(userId, failureCount, timeWindow) {
    await this.sendAnomalyAlert('brute_force_attempt', {
      userId,
      failureCount,
      timeWindow,
      severity: 'high',
      action_required: 'Consider blocking IP or user account'
    });
  }
  
  /**
   * 权限提升告警
   */
  static async sendPrivilegeEscalationAlert(userId, oldRole, newRole) {
    await this.sendAnomalyAlert('privilege_escalation', {
      userId,
      oldRole,
      newRole,
      severity: 'critical',
      action_required: 'Verify authorization for role change'
    });
  }
  
  /**
   * 异常数据访问告警
   */
  static async sendDataAccessAlert(accessorId, targetUserId, dataType) {
    await this.sendAnomalyAlert('unusual_data_access', {
      accessorId,
      targetUserId,
      dataType,
      severity: 'medium',
      compliance_flag: 'GDPR'
    });
  }
  
  /**
   * 映射风险级别到Sentry级别
   */
  static mapRiskToSentryLevel(riskLevel) {
    const mapping = {
      'LOW': 'info',
      'MEDIUM': 'warning', 
      'HIGH': 'error',
      'CRITICAL': 'fatal'
    };
    return mapping[riskLevel] || 'info';
  }
  
  /**
   * 创建自定义性能监控
   */
  static startAuditTransaction(operationName) {
    return Sentry.startTransaction({
      name: `audit_${operationName}`,
      op: 'audit_operation'
    });
  }
}

/**
 * 增强的审计日志服务（集成Sentry）
 */
class EnhancedAuditLogService extends AuditLogService {
  
  /**
   * 记录审计日志并发送到Sentry
   */
  static async log(auditData) {
    const transaction = SentryAuditIntegration.startAuditTransaction(
      auditData.action.operation
    );
    
    try {
      // 记录到数据库
      const auditLog = await super.log(auditData);
      
      // 高风险操作发送到Sentry
      if (['HIGH', 'CRITICAL'].includes(auditData.riskLevel)) {
        await SentryAuditIntegration.sendSecurityEvent(auditData);
      }
      
      // 检查异常模式
      await this.checkAnomalies(auditData);
      
      transaction.setStatus('ok');
      return auditLog;
      
    } catch (error) {
      transaction.setStatus('internal_error');
      Sentry.captureException(error);
      throw error;
    } finally {
      transaction.finish();
    }
  }
  
  /**
   * 检查异常操作模式
   */
  static async checkAnomalies(auditData) {
    const { actor, action, result } = auditData;
    
    // 检查登录失败次数
    if (action.category === 'AUTH' && result.status === 'FAILURE') {
      const recentFailures = await this.getRecentFailures(actor.userId, 15); // 15分钟内
      if (recentFailures >= 5) {
        await SentryAuditIntegration.sendBruteForceAlert(
          actor.userId, 
          recentFailures, 
          '15 minutes'
        );
      }
    }
    
    // 检查异常时间访问
    const hour = new Date().getHours();
    if ((hour < 6 || hour > 22) && action.category === 'ADMIN') {
      await SentryAuditIntegration.sendAnomalyAlert('off_hours_admin_access', {
        userId: actor.userId,
        hour,
        operation: action.operation
      });
    }
    
    // 检查大量数据访问
    if (action.category === 'DATA_ACCESS') {
      const recentAccess = await this.getRecentDataAccess(actor.userId, 60); // 1小时内
      if (recentAccess >= 100) {
        await SentryAuditIntegration.sendDataAccessAlert(
          actor.userId,
          auditData.target?.resourceId,
          auditData.target?.resourceType
        );
      }
    }
  }
  
  /**
   * 获取最近失败次数
   */
  static async getRecentFailures(userId, minutes) {
    const since = new Date(Date.now() - minutes * 60 * 1000);
    const count = await AuditLog.countDocuments({
      'actor.userId': userId,
      'action.category': 'AUTH',
      'result.status': 'FAILURE',
      timestamp: { $gte: since }
    });
    return count;
  }
  
  /**
   * 获取最近数据访问次数
   */
  static async getRecentDataAccess(userId, minutes) {
    const since = new Date(Date.now() - minutes * 60 * 1000);
    const count = await AuditLog.countDocuments({
      'actor.userId': userId,
      'action.category': 'DATA_ACCESS',
      timestamp: { $gte: since }
    });
    return count;
  }
}

/**
 * Sentry错误边界中间件
 */
const sentryErrorHandler = (error, req, res, next) => {
  // 记录到审计日志
  EnhancedAuditLogService.log({
    actor: {
      userId: req.userId || 'anonymous',
      userType: req.userType || 'unknown'
    },
    action: {
      type: 'ERROR',
      category: 'SYSTEM',
      operation: 'system_error',
      description: 'System error occurred'
    },
    request: {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      method: req.method,
      endpoint: req.path
    },
    result: {
      status: 'ERROR',
      errorMessage: error.message
    },
    riskLevel: 'MEDIUM'
  });
  
  // 发送到Sentry
  Sentry.captureException(error);
  
  next(error);
};

module.exports = {
  SentryAuditIntegration,
  EnhancedAuditLogService,
  sentryErrorHandler
};
